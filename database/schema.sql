-- MySQL数据库表结构设计
-- 用于xuexitong_pro座位预约系统

-- 创建数据库
CREATE DATABASE IF NOT EXISTS xuexitong_reserve 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE xuexitong_reserve;

-- 预约配置表
CREATE TABLE reservations (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    workerid VARCHAR(50) NOT NULL COMMENT '服务器工作ID，用于多服务器部署',
    username VARCHAR(50) NOT NULL COMMENT '学习通账号',
    password VARCHAR(255) NOT NULL COMMENT '学习通密码',
    roomid VARCHAR(20) NOT NULL COMMENT '房间ID',
    seatid JSON NOT NULL COMMENT '座位号列表，JSON格式存储',
    starttime TIME NOT NULL COMMENT '开始时间',
    endtime TIME NOT NULL COMMENT '结束时间',
    daysofweek JSON NOT NULL COMMENT '预约星期，JSON格式存储',
    open_reserve_time TIME DEFAULT '07:00:00' COMMENT '开放预约时间',
    reservation_type ENUM('today', 'tomorrow') DEFAULT 'tomorrow' COMMENT '预约类型：今天或明天',
    max_reservation_hours INT DEFAULT 12 COMMENT '最大预约小时数',
    enable_slider BOOLEAN DEFAULT FALSE COMMENT '是否启用滑块验证',
    max_attempt INT DEFAULT 3 COMMENT '最大尝试次数',
    sleep_time DECIMAL(3,2) DEFAULT 0.20 COMMENT '每次尝试间隔时间(秒)',
    comment VARCHAR(255) DEFAULT '' COMMENT '备注信息',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_workerid (workerid),
    INDEX idx_active (is_active),
    INDEX idx_reservation_type (reservation_type),
    INDEX idx_created_at (created_at)
) COMMENT='座位预约配置表';

-- 预约日志表（可选，用于记录预约历史）
CREATE TABLE reservation_logs (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    reservation_id INT NOT NULL COMMENT '预约配置ID',
    username VARCHAR(50) NOT NULL COMMENT '学习通账号',
    roomid VARCHAR(20) NOT NULL COMMENT '房间ID',
    seatid VARCHAR(10) NOT NULL COMMENT '座位号',
    reserve_date DATE NOT NULL COMMENT '预约日期',
    start_time TIME NOT NULL COMMENT '开始时间',
    end_time TIME NOT NULL COMMENT '结束时间',
    status ENUM('success', 'failed', 'error') NOT NULL COMMENT '预约状态',
    error_message TEXT COMMENT '错误信息',
    attempt_count INT DEFAULT 1 COMMENT '尝试次数',
    execution_time DECIMAL(5,2) COMMENT '执行耗时(秒)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (reservation_id) REFERENCES reservations(id) ON DELETE CASCADE,
    INDEX idx_reservation_id (reservation_id),
    INDEX idx_username (username),
    INDEX idx_reserve_date (reserve_date),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) COMMENT='预约执行日志表';

-- 系统配置表（可选，用于存储全局配置）
CREATE TABLE system_config (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT NOT NULL COMMENT '配置值',
    description VARCHAR(255) COMMENT '配置描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='系统配置表';

-- 插入默认系统配置
INSERT INTO system_config (config_key, config_value, description) VALUES
('default_sleep_time', '0.2', '默认请求间隔时间'),
('default_max_attempt', '3', '默认最大尝试次数'),
('default_end_time', '07:01:00', '默认结束时间'),
('enable_logging', 'true', '是否启用详细日志');

-- 示例数据插入（基于现有config.json）
INSERT INTO reservations (
    workerid, username, password, roomid, seatid, 
    starttime, endtime, daysofweek, comment, 
    reservation_type, max_reservation_hours
) VALUES 
(
    'server1', 
    '18755869972', 
    'tcc123698741', 
    '4991', 
    '["2"]',
    '15:00:00', 
    '19:00:00',
    '["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]',
    '测试用户: 与Java版本相同的参数',
    'tomorrow',
    12
);
