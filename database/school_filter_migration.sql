-- 学校筛选功能数据库迁移脚本
-- 用于添加学校表和相关字段

-- 创建学校配置表
CREATE TABLE IF NOT EXISTS schools (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '学校ID',
    school_code VARCHAR(50) NOT NULL UNIQUE COMMENT '学校代码',
    school_name VARCHAR(100) NOT NULL COMMENT '学校名称',
    base_url VARCHAR(255) NOT NULL COMMENT '预约系统基础URL',
    wait_time DECIMAL(3,2) NOT NULL DEFAULT 0.50 COMMENT '服务器响应等待时间(秒)',
    max_reservation_hours INT DEFAULT 4 COMMENT '最大预约小时数',
    timezone VARCHAR(50) DEFAULT 'Asia/Shanghai' COMMENT '时区',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_school_code (school_code),
    INDEX idx_active (is_active),
    CONSTRAINT chk_wait_time CHECK (wait_time >= 0.10 AND wait_time <= 1.00)
) COMMENT='学校配置表';

-- 插入示例学校数据
INSERT IGNORE INTO schools (school_code, school_name, base_url, wait_time) VALUES
('AHNU', '安徽师范大学', 'https://office.chaoxing.com', 0.30),
('HFUT', '合肥工业大学', 'https://office.chaoxing.com', 0.50),
('USTC', '中国科学技术大学', 'https://office.chaoxing.com', 0.20),
('DEFAULT', '默认学校', 'https://office.chaoxing.com', 0.50);

-- 检查 reservations 表是否已有 school_id 字段
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'reservations'
    AND COLUMN_NAME = 'school_id'
);

-- 如果 school_id 字段不存在，则添加
SET @sql = IF(@column_exists = 0,
    'ALTER TABLE reservations ADD COLUMN school_id INT COMMENT "学校ID", ADD FOREIGN KEY (school_id) REFERENCES schools(id)',
    'SELECT "school_id 字段已存在" AS message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 创建索引（如果不存在）
SET @index_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.STATISTICS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'reservations'
    AND INDEX_NAME = 'idx_school_id'
);

SET @sql = IF(@index_exists = 0,
    'CREATE INDEX idx_school_id ON reservations(school_id)',
    'SELECT "idx_school_id 索引已存在" AS message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为现有的 reservations 记录设置默认学校（如果 school_id 为 NULL）
UPDATE reservations 
SET school_id = (SELECT id FROM schools WHERE school_code = 'DEFAULT' LIMIT 1)
WHERE school_id IS NULL;

-- 检查 rooms 表是否已有 school_id 字段
SET @room_column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'rooms'
    AND COLUMN_NAME = 'school_id'
);

-- 如果 rooms 表存在且没有 school_id 字段，则添加
SET @sql = IF(@room_column_exists = 0 AND (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'rooms') > 0,
    'ALTER TABLE rooms ADD COLUMN school_id INT COMMENT "学校ID", ADD FOREIGN KEY (school_id) REFERENCES schools(id)',
    'SELECT "rooms 表不存在或 school_id 字段已存在" AS message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查 rooms 表是否已有 captcha 字段
SET @captcha_column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'rooms'
    AND COLUMN_NAME = 'captcha'
);

-- 如果 rooms 表存在且没有 captcha 字段，则添加
SET @sql = IF(@captcha_column_exists = 0 AND (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'rooms') > 0,
    'ALTER TABLE rooms ADD COLUMN captcha BOOLEAN DEFAULT FALSE COMMENT "是否启用验证码"',
    'SELECT "rooms 表不存在或 captcha 字段已存在" AS message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 显示迁移结果
SELECT 'schools 表记录数' AS description, COUNT(*) AS count FROM schools
UNION ALL
SELECT 'reservations 表记录数', COUNT(*) FROM reservations
UNION ALL
SELECT 'reservations 表有 school_id 的记录数', COUNT(*) FROM reservations WHERE school_id IS NOT NULL;

-- 显示学校配置
SELECT 
    school_code,
    school_name,
    wait_time,
    is_active,
    (SELECT COUNT(*) FROM reservations WHERE school_id = schools.id) AS reservation_count
FROM schools
ORDER BY school_code;
