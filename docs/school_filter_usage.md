# 学校筛选功能使用指南

## 📋 功能概述

学校筛选功能允许您根据学校配置来筛选和执行预约任务，实现更精细的任务管理。

### 主要特性

- **学校级别筛选**：只处理指定学校的预约任务
- **多学校支持**：可同时处理多个学校的任务
- **向后兼容**：如果不配置学校筛选，则处理所有任务
- **灵活配置**：支持配置文件、环境变量和命令行参数
- **自动检测**：自动检测数据库是否支持学校筛选功能
- **动态验证码**：基于房间配置自动启用/禁用滑动验证

## 🚀 快速开始

### 1. 数据库准备

首先需要创建学校表和相关字段：

```bash
# 执行数据库迁移脚本
mysql -u your_username -p your_database < database/school_filter_migration.sql
```

### 2. 配置学校筛选

#### 方式1：配置文件

在配置文件中添加学校筛选配置：

```json
{
  "school_filter": {
    "enabled": true,
    "school_names": ["安徽师范大学", "合肥工业大学"],
    "fallback_to_all": true
  }
}
```

#### 方式2：环境变量

```bash
export SCHOOL_FILTER_ENABLED=true
export SCHOOL_FILTER_NAMES=安徽师范大学,合肥工业大学
export SCHOOL_FILTER_FALLBACK=true
```

#### 方式3：命令行参数

```bash
# 处理指定学校的任务
python main_db.py --schools 安徽师范大学,合肥工业大学

# 处理单个学校的任务
python main_db.py --schools 安徽师范大学

# 不指定学校（处理所有任务）
python main_db.py
```

## 📖 详细使用说明

### 配置参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `enabled` | boolean | false | 是否启用学校筛选 |
| `school_names` | array | [] | 要处理的学校名称列表 |
| `fallback_to_all` | boolean | true | 数据库不支持时是否回退到处理所有任务 |

### 学校名称说明

系统预设了以下学校：

- `安徽师范大学` - 学校代码：AHNU
- `合肥工业大学` - 学校代码：HFUT
- `中国科学技术大学` - 学校代码：USTC
- `默认学校` - 学校代码：DEFAULT

您可以在 `schools` 表中添加更多学校。筛选时使用学校的完整名称。

### 验证码配置说明

系统支持基于房间配置的动态验证码启用：

- **数据库字段**：`rooms.captcha` (BOOLEAN类型)
- **启用条件**：`captcha = TRUE` 或 `captcha = 1`
- **禁用条件**：`captcha = FALSE`、`captcha = 0` 或 `captcha = NULL`
- **向后兼容**：如果 `rooms` 表没有 `captcha` 字段，默认禁用验证码

### 使用场景

#### 场景1：多服务器部署，按学校分配

```bash
# 服务器1处理安徽师范大学
python main_db.py --worker server1 --schools 安徽师范大学

# 服务器2处理合肥工业大学
python main_db.py --worker server2 --schools 合肥工业大学

# 服务器3处理中科大
python main_db.py --worker server3 --schools 中国科学技术大学
```

#### 场景2：单服务器处理多个学校

```bash
# 处理多个学校
python main_db.py --schools 安徽师范大学,合肥工业大学,中国科学技术大学
```

#### 场景3：测试特定学校

```bash
# 测试安徽师范大学的预约任务
python main_db.py --method debug --schools 安徽师范大学
```

## 🔍 日志和监控

### 日志输出示例

启用学校筛选后，日志会显示详细的学校信息：

```
2025-07-21 10:00:00 - INFO - 设置学校筛选: ['安徽师范大学', '合肥工业大学']
2025-07-21 10:00:01 - INFO - 数据库支持学校筛选功能
2025-07-21 10:00:02 - INFO - 数据库查询结果 (3 条记录，筛选学校: ['安徽师范大学', '合肥工业大学']):
2025-07-21 10:00:02 - INFO - 记录1: ID=1 [学校: 安徽师范大学]
2025-07-21 10:00:02 - INFO - 记录2: ID=2 [学校: 合肥工业大学]
2025-07-21 10:00:02 - INFO - 记录3: ID=3 [学校: 安徽师范大学]
2025-07-21 10:00:03 - INFO - 获取到 3 条活跃预约配置 (worker_id: server1, 筛选学校: ['安徽师范大学', '合肥工业大学'])
2025-07-21 10:00:03 - INFO - 学校任务分布:
2025-07-21 10:00:03 - INFO -   - 安徽师范大学: 2 个任务
2025-07-21 10:00:03 - INFO -   - 合肥工业大学: 1 个任务
```

### 任务详情显示

```
2025-07-21 10:00:04 - INFO - 任务1: 学校安徽师范大学_用户18755869972_房间4991_座位2 [安徽师范大学]
2025-07-21 10:00:04 - INFO -   - 账号: 18755869972
2025-07-21 10:00:04 - INFO -   - 房间: 4991
2025-07-21 10:00:04 - INFO -   - 座位: ['2']
2025-07-21 10:00:04 - INFO -   - 时间段: [['15:00', '19:00']]
2025-07-21 10:00:04 - INFO -   - 滑动验证: 启用
```

## 🛠️ 测试和调试

### 运行测试脚本

```bash
# 测试学校筛选功能
python test_school_filter.py

# 测试验证码功能
python test_captcha_feature.py
```

### 查看统计信息

```bash
# 查看预约统计（包含学校信息）
python main_db.py --method stats
```

## ⚠️ 注意事项

### 向后兼容性

- 如果数据库中没有 `schools` 表或 `school_id` 字段，系统会自动使用原有逻辑
- 现有的预约任务不会受到影响
- 可以逐步迁移到学校筛选模式

### 性能考虑

- 学校筛选查询会关联 `schools` 表，可能略微影响查询性能
- 建议在 `reservations.school_id` 和 `schools.school_code` 上创建索引

### 错误处理

- 如果指定的学校代码不存在，系统会记录警告但不会中断执行
- 如果数据库连接失败，系统会回退到原有逻辑

## 📚 API 参考

### DatabaseReservationManager

```python
# 设置学校筛选
manager.set_school_filter(['安徽师范大学', '合肥工业大学'])

# 加载预约配置（自动应用学校筛选）
reservations = manager.load_reservations_from_database()
```

### ReservationRepository

```python
# 获取指定学校的预约配置
reservations = repository.get_active_reservations_by_worker(
    worker_id='server1',
    school_names=['安徽师范大学', '合肥工业大学']
)

# 检查数据库是否支持学校筛选
supports = repository._check_school_support()
```

## 🔧 故障排除

### 常见问题

1. **学校筛选不生效**
   - 检查数据库是否有 `schools` 表和 `school_id` 字段
   - 确认学校代码是否正确
   - 查看日志中的数据库支持检查结果

2. **没有找到预约任务**
   - 确认指定的学校代码在数据库中存在
   - 检查 `reservations` 表中的 `school_id` 字段是否正确设置
   - 验证 `worker_id` 是否匹配

3. **性能问题**
   - 确保在相关字段上创建了索引
   - 考虑优化查询条件
   - 监控数据库查询性能

### 调试命令

```bash
# 查看数据库表结构
mysql -u username -p -e "DESCRIBE schools; DESCRIBE reservations;"

# 查看学校数据
mysql -u username -p -e "SELECT * FROM schools;"

# 查看预约任务的学校分布
mysql -u username -p -e "
SELECT s.school_code, s.school_name, COUNT(r.id) as task_count 
FROM schools s 
LEFT JOIN reservations r ON s.id = r.school_id 
GROUP BY s.id;"
```
