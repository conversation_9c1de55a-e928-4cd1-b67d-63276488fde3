from cryptography.hazmat.primitives import padding
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
import base64
from hashlib import md5
import random
from uuid import uuid1
import logging

def AES_Encrypt(data):
    key = b"u2oh6Vu^HWe4_AES"  # Convert to bytes
    iv = b"u2oh6Vu^HWe4_AES"  # Convert to bytes
    padder = padding.PKCS7(128).padder()
    padded_data = padder.update(data.encode('utf-8')) + padder.finalize()
    cipher = Cipher(algorithms.AES(key), modes.CBC(iv), backend=default_backend())
    encryptor = cipher.encryptor()
    encrypted_data = encryptor.update(padded_data) + encryptor.finalize()
    enctext = base64.b64encode(encrypted_data).decode('utf-8')
    return enctext
    
def resort(submit_info):
    return {key: submit_info[key] for key in sorted(submit_info.keys())}

def enc(submit_info, submit_enc_value=None):
    """
    生成请求参数的加密签名

    Args:
        submit_info: 请求参数字典
        submit_enc_value: 页面提取的 submit_enc 值（可选）

    Returns:
        str: MD5 加密签名
    """
    try:
        add = lambda x, y: x + y
        processed_info = resort(submit_info)
        needed = [add(add('[', key), '=' + str(value)) + ']' for key, value in processed_info.items()]

        # 如果提供了 submit_enc_value，使用它；否则使用默认模式
        if submit_enc_value:
            needed.append(add('[', submit_enc_value) + ']')
            logging.info(f"使用 submit_enc 值进行加密: {submit_enc_value}")
        else:
            pattern = "%sd`~7^/>N4!Q#){''"
            needed.append(add('[', pattern) + ']')
            logging.info("使用默认模式进行加密")

        seq = ''.join(needed)
        result = md5(seq.encode("utf-8")).hexdigest()

        logging.info(f"加密字符串: {seq}")
        logging.info(f"MD5 结果: {result}")

        return result

    except Exception as e:
        logging.error(f"加密过程中发生错误: {e}")
        # 回退到原始逻辑
        add = lambda x, y: x + y
        processed_info = resort(submit_info)
        needed = [add(add('[', key), '=' + str(value)) + ']' for key, value in processed_info.items()]
        pattern = "%sd`~7^/>N4!Q#){''"
        needed.append(add('[', pattern) + ']')
        seq = ''.join(needed)
        return md5(seq.encode("utf-8")).hexdigest()


def generate_captcha_key(timestamp: int):
    captcha_key = md5((str(timestamp) + str(uuid1())).encode("utf-8")).hexdigest()
    encoded_timestamp = md5(
        (str(timestamp) + "42sxgHoTPTKbt0uZxPJ7ssOvtXr3ZgZ1" + "slide" + captcha_key).encode("utf-8")
    ).hexdigest() + ":" + str(int(timestamp) + 0x493e0)
    return [captcha_key, encoded_timestamp]
