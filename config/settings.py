"""
配置管理模块
支持从环境变量和配置文件读取配置
"""
import os
import json
import logging
from typing import Dict, Any, Optional

class Config:
    """配置管理类"""
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.logger = logging.getLogger(__name__)
        self._config = {}
        self._load_default_config()
        
        if config_file and os.path.exists(config_file):
            self._load_config_file(config_file)
        
        self._load_env_config()
    
    def _load_default_config(self):
        """加载默认配置"""
        self._config = {
            # 数据库配置
            'database': {
                'host': 'localhost',
                'port': 3306,
                'user': 'root',
                'password': 'root',
                'database': 'seat_reservation',
                'charset': 'utf8mb4'
            },
            
            # 预约系统配置
            'reservation': {
                'sleep_time': 0.2,
                'max_attempt': 1,
                'enable_slider': False,
                'reserve_next_day': False,
                'random_wait_range': {
                    'min': 0,
                    'max': 3
                }
            },
            
            # 服务器配置
            'server': {
                'worker_id': 'worker-8083'
            },

            # 学校筛选配置
            'school_filter': {
                'enabled': False,  # 是否启用学校筛选
                'school_names': [],  # 要处理的学校名称列表，空列表表示处理所有学校
                'fallback_to_all': True  # 如果数据库不支持学校筛选，是否回退到处理所有任务
            },
            
            # 日志配置
            'logging': {
                'level': 'INFO',
                'format': '%(asctime)s - %(levelname)s - %(message)s',
                'enable_file_log': True,
                'log_dir': 'logs'
            },

            # 心跳配置
            'heartbeat': {
                'main_server_url': 'http://localhost:8081',
                'worker_host': 'localhost',  # 副服务器主机地址
                'worker_port': 8082,         # 副服务器端口
                'worker_name': 'SeatMaster Worker Server',  # 服务器名称
                'max_concurrent_tasks': 10,  # 最大并发任务数
                'supported_operations': ['XUEXITONG_RESERVATION'],  # 支持的操作
                'interval': 30,  # 心跳发送间隔（秒）
                'timeout': 5,    # 请求超时时间（秒）
                'retry_count': 1,  # 重试次数
                'status': 'ONLINE',  # 默认状态
                'current_load': 0    # 默认负载
            }
        }
    
    def _load_config_file(self, config_file: str):
        """从配置文件加载配置"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                file_config = json.load(f)
            
            # 深度合并配置
            self._deep_merge(self._config, file_config)
            self.logger.info(f"已加载配置文件: {config_file}")
            
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
    
    def _load_env_config(self):
        """从环境变量加载配置"""
        env_mappings = {
            'DB_HOST': ('database', 'host'),
            'DB_PORT': ('database', 'port'),
            'DB_USER': ('database', 'user'),
            'DB_PASSWORD': ('database', 'password'),
            'DB_DATABASE': ('database', 'database'),
            'WORKER_ID': ('server', 'worker_id'),
            'SLEEP_TIME': ('reservation', 'sleep_time'),
            'MAX_ATTEMPT': ('reservation', 'max_attempt'),
            'ENABLE_SLIDER': ('reservation', 'enable_slider'),
            'RESERVE_NEXT_DAY': ('reservation', 'reserve_next_day'),
            'LOG_LEVEL': ('logging', 'level'),
            'SCHOOL_FILTER_ENABLED': ('school_filter', 'enabled'),
            'SCHOOL_FILTER_NAMES': ('school_filter', 'school_names'),
            'SCHOOL_FILTER_FALLBACK': ('school_filter', 'fallback_to_all'),
            'HEARTBEAT_MAIN_SERVER_URL': ('heartbeat', 'main_server_url'),
            'HEARTBEAT_WORKER_HOST': ('heartbeat', 'worker_host'),
            'HEARTBEAT_WORKER_PORT': ('heartbeat', 'worker_port'),
            'HEARTBEAT_WORKER_NAME': ('heartbeat', 'worker_name'),
            'HEARTBEAT_MAX_CONCURRENT_TASKS': ('heartbeat', 'max_concurrent_tasks'),
            'HEARTBEAT_INTERVAL': ('heartbeat', 'interval'),
            'HEARTBEAT_TIMEOUT': ('heartbeat', 'timeout'),
            'HEARTBEAT_RETRY_COUNT': ('heartbeat', 'retry_count'),
            'HEARTBEAT_STATUS': ('heartbeat', 'status'),
            'HEARTBEAT_CURRENT_LOAD': ('heartbeat', 'current_load')
        }
        
        for env_key, (section, key) in env_mappings.items():
            env_value = os.getenv(env_key)
            if env_value is not None:
                # 类型转换
                if key in ['port', 'max_attempt']:
                    env_value = int(env_value)
                elif key in ['sleep_time']:
                    env_value = float(env_value)
                elif key in ['enable_slider', 'reserve_next_day', 'enable_json_fallback', 'enabled', 'fallback_to_all']:
                    env_value = env_value.lower() in ('true', '1', 'yes', 'on')
                elif key in ['interval', 'timeout', 'retry_count', 'current_load', 'worker_port', 'max_concurrent_tasks']:
                    env_value = int(env_value)
                elif key == 'supported_operations' and env_value:
                    # 解析逗号分隔的操作列表
                    env_value = [op.strip() for op in env_value.split(',') if op.strip()]
                elif key == 'school_names' and env_value:
                    # 解析逗号分隔的学校名称列表
                    env_value = [name.strip() for name in env_value.split(',') if name.strip()]
                
                self._config[section][key] = env_value
                self.logger.debug(f"从环境变量加载配置: {env_key} = {env_value}")
    
    def _deep_merge(self, base_dict: dict, update_dict: dict):
        """深度合并字典"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_merge(base_dict[key], value)
            else:
                base_dict[key] = value
    
    def get(self, section: str, key: str = None, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            section: 配置节
            key: 配置键
            default: 默认值
            
        Returns:
            配置值
        """
        if key is None:
            return self._config.get(section, default)
        
        section_config = self._config.get(section, {})
        return section_config.get(key, default)
    
    def get_database_config(self) -> Dict[str, Any]:
        """获取数据库配置"""
        return self._config.get('database', {})
    
    def get_reservation_config(self) -> Dict[str, Any]:
        """获取预约配置"""
        return self._config.get('reservation', {})
    
    def get_random_wait_range(self) -> Dict[str, int]:
        """获取随机等待时间范围配置"""
        return self.get('reservation', 'random_wait_range', {'min': 0, 'max': 3})
    
    def get_server_config(self) -> Dict[str, Any]:
        """获取服务器配置"""
        return self._config.get('server', {})
    
    def get_worker_id(self) -> str:
        """获取工作节点ID"""
        return self.get('server', 'worker_id', 'server1')

    def get_school_filter_config(self) -> Dict[str, Any]:
        """获取学校筛选配置"""
        return self._config.get('school_filter', {})

    def is_school_filter_enabled(self) -> bool:
        """检查是否启用学校筛选"""
        return self.get('school_filter', 'enabled', False)

    def get_school_names(self) -> list:
        """获取要处理的学校名称列表"""
        return self.get('school_filter', 'school_names', [])

    def get_heartbeat_config(self) -> Dict[str, Any]:
        """获取心跳配置"""
        return self._config.get('heartbeat', {})

    def get_heartbeat_main_server_url(self) -> str:
        """获取主服务器URL"""
        return self.get('heartbeat', 'main_server_url', 'http://localhost:8081')

    def get_heartbeat_interval(self) -> int:
        """获取心跳发送间隔（秒）"""
        return self.get('heartbeat', 'interval', 30)

    def get_heartbeat_timeout(self) -> int:
        """获取心跳请求超时时间（秒）"""
        return self.get('heartbeat', 'timeout', 5)

    def get_heartbeat_retry_count(self) -> int:
        """获取心跳重试次数"""
        return self.get('heartbeat', 'retry_count', 1)

    def get_heartbeat_status(self) -> str:
        """获取心跳状态"""
        return self.get('heartbeat', 'status', 'ONLINE')

    def get_heartbeat_current_load(self) -> int:
        """获取当前负载"""
        return self.get('heartbeat', 'current_load', 0)

    def get_heartbeat_worker_host(self) -> str:
        """获取副服务器主机地址"""
        return self.get('heartbeat', 'worker_host', 'localhost')

    def get_heartbeat_worker_port(self) -> int:
        """获取副服务器端口"""
        return self.get('heartbeat', 'worker_port', 8082)

    def get_heartbeat_worker_name(self) -> str:
        """获取服务器名称"""
        return self.get('heartbeat', 'worker_name', 'SeatMaster Worker Server')

    def get_heartbeat_max_concurrent_tasks(self) -> int:
        """获取最大并发任务数"""
        return self.get('heartbeat', 'max_concurrent_tasks', 10)

    def get_heartbeat_supported_operations(self) -> list:
        """获取支持的操作列表"""
        return self.get('heartbeat', 'supported_operations', ['XUEXITONG_RESERVATION'])
    
    def validate_config(self) -> bool:
        """验证配置完整性"""
        required_configs = [
            ('database', 'host'),
            ('database', 'user'),
            ('database', 'password'),
            ('database', 'database'),
            ('server', 'worker_id'),
            ('heartbeat', 'main_server_url')
        ]
        
        for section, key in required_configs:
            value = self.get(section, key)
            if not value:
                self.logger.error(f"缺少必需配置: {section}.{key}")
                return False
        
        return True
    
    def __str__(self) -> str:
        """返回配置的字符串表示（隐藏敏感信息）"""
        safe_config = self._config.copy()
        if 'database' in safe_config and 'password' in safe_config['database']:
            safe_config['database']['password'] = '***'
        return json.dumps(safe_config, indent=2, ensure_ascii=False)


# 全局配置实例
# 尝试加载数据库配置文件
config_file = None
for potential_file in ['database_config.json', 'config/database.json']:
    if os.path.exists(potential_file):
        config_file = potential_file
        break

config = Config(config_file)
